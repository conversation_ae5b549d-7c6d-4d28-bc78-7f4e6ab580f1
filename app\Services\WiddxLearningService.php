<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\KnowledgeEntry;
use App\Models\ConversationInsight;
use App\Models\UserPattern;
use App\Models\PersonalityEvolution;
use App\Models\ContextMemory;
use App\Services\DeepSeekService;
use App\Services\GeminiService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class WiddxLearningService
{
    private DeepSeekService $deepSeek;
    private GeminiService $gemini;
    private WiddxKnowledgeService $knowledge;

    public function __construct(
        DeepSeekService $deepSeek,
        GeminiService $gemini,
        WiddxKnowledgeService $knowledge
    ) {
        $this->deepSeek = $deepSeek;
        $this->gemini = $gemini;
        $this->knowledge = $knowledge;
    }

    /**
     * Analyze conversation and extract learning opportunities
     */
    public function analyzeConversation(
        ChatSession $session,
        string $userMessage,
        string $widdxResponse,
        array $context = []
    ): array {
        try {
            $language = $context['language'] ?? 'en';
            
            // Use DeepSeek for conversation analysis
            $analysisPrompt = $this->buildAnalysisPrompt($userMessage, $widdxResponse, $language);
            
            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => $this->getAnalysisSystemPrompt($language)],
                ['role' => 'user', 'content' => $analysisPrompt],
            ], [
                'max_tokens' => 1000,
                'temperature' => 0.3,
            ]);

            if ($response['success']) {
                return $this->parseAnalysisResponse($response['content'], $language);
            }

            return $this->getFallbackAnalysis($userMessage, $widdxResponse, $language);

        } catch (\Exception $e) {
            Log::error('Conversation analysis failed', [
                'error' => $e->getMessage(),
                'session_id' => $session->session_id,
            ]);

            return $this->getFallbackAnalysis($userMessage, $widdxResponse, $language);
        }
    }

    /**
     * Extract user preferences from conversation
     */
    public function extractUserPreferences(string $userMessage, string $language = 'en'): array
    {
        $preferences = [];

        // Language preference patterns
        $languagePatterns = [
            'en' => ['I like', 'I prefer', 'I love', 'I enjoy', 'My favorite'],
            'ar' => ['أحب', 'أفضل', 'يعجبني', 'أستمتع بـ', 'المفضل لدي'],
        ];

        $patterns = $languagePatterns[$language] ?? $languagePatterns['en'];

        foreach ($patterns as $pattern) {
            if (stripos($userMessage, $pattern) !== false) {
                $preference = $this->extractPreferenceContext($userMessage, $pattern, $language);
                if ($preference) {
                    $preferences[] = [
                        'type' => 'preference',
                        'content' => $preference,
                        'confidence' => 0.8,
                        'language' => $language,
                    ];
                }
            }
        }

        return $preferences;
    }

    /**
     * Analyze user communication style
     */
    public function analyzeCommunicationStyle(string $userMessage, string $language = 'en'): array
    {
        $style = [];

        // Formality analysis
        $formalIndicators = [
            'en' => ['please', 'thank you', 'could you', 'would you'],
            'ar' => ['من فضلك', 'شكرا', 'هل يمكنك', 'هل تستطيع'],
        ];

        $casualIndicators = [
            'en' => ['hey', 'hi', 'yeah', 'ok', 'cool'],
            'ar' => ['مرحبا', 'أهلا', 'نعم', 'حسنا', 'رائع'],
        ];

        $formal = $formalIndicators[$language] ?? $formalIndicators['en'];
        $casual = $casualIndicators[$language] ?? $casualIndicators['en'];

        $formalCount = 0;
        $casualCount = 0;

        foreach ($formal as $indicator) {
            if (stripos($userMessage, $indicator) !== false) {
                $formalCount++;
            }
        }

        foreach ($casual as $indicator) {
            if (stripos($userMessage, $indicator) !== false) {
                $casualCount++;
            }
        }

        if ($formalCount > $casualCount) {
            $style['formality'] = 'formal';
        } elseif ($casualCount > $formalCount) {
            $style['formality'] = 'casual';
        } else {
            $style['formality'] = 'neutral';
        }

        // Question frequency
        $questionCount = substr_count($userMessage, '?');
        if ($questionCount > 2) {
            $style['inquiry_level'] = 'high';
        } elseif ($questionCount > 0) {
            $style['inquiry_level'] = 'medium';
        } else {
            $style['inquiry_level'] = 'low';
        }

        return $style;
    }

    /**
     * Update WIDDX personality based on interactions
     */
    public function updatePersonality(array $interactionData, string $language = 'en'): array
    {
        $updates = [];

        try {
            // Analyze if WIDDX should adapt its communication style
            $userStyle = $interactionData['user_style'] ?? [];
            
            if (!empty($userStyle)) {
                $personalityAspect = PersonalityEvolution::aspect('communication_style')->first();
                
                if (!$personalityAspect) {
                    $personalityAspect = PersonalityEvolution::create([
                        'aspect' => 'communication_style',
                        'current_state' => ['formality' => 'neutral', 'adaptability' => 0.5],
                        'stability_score' => 0.5,
                        'last_updated_at' => now(),
                    ]);
                }

                // Adapt to user's formality level gradually
                $currentState = $personalityAspect->current_state;
                $userFormality = $userStyle['formality'] ?? 'neutral';

                if ($userFormality !== $currentState['formality']) {
                    $adaptability = $currentState['adaptability'] ?? 0.5;
                    
                    // Gradually shift towards user's style
                    if ($adaptability > 0.3) {
                        $newState = $currentState;
                        $newState['formality'] = $userFormality;
                        $newState['adaptation_reason'] = "Adapting to user's {$userFormality} communication style";
                        
                        $personalityAspect->updateAspect($newState, "User communication style adaptation");
                        $updates[] = "Adapted communication style to {$userFormality}";
                    }
                }
            }

            // Update language expertise
            $this->updateLanguageExpertise($language);

        } catch (\Exception $e) {
            Log::error('Personality update failed', ['error' => $e->getMessage()]);
        }

        return $updates;
    }

    /**
     * Store contextual memory for future reference
     */
    public function storeContextMemory(string $key, $value, string $scope = 'global', ?string $scopeIdentifier = null): void
    {
        ContextMemory::updateOrCreate([
            'key' => $key,
            'scope' => $scope,
            'scope_identifier' => $scopeIdentifier,
        ], [
            'memory_type' => 'conversation_context',
            'value' => is_array($value) ? $value : ['content' => $value],
            'importance' => 0.7,
            'expires_at' => now()->addDays(30),
        ]);
    }

    /**
     * Retrieve contextual memory
     */
    public function getContextMemory(string $key, string $scope = 'global', ?string $scopeIdentifier = null): ?array
    {
        $memory = ContextMemory::where('key', $key)
            ->where('scope', $scope)
            ->where('scope_identifier', $scopeIdentifier)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->first();

        return $memory ? $memory->value : null;
    }

    /**
     * Build analysis prompt for DeepSeek
     */
    private function buildAnalysisPrompt(string $userMessage, string $widdxResponse, string $language): string
    {
        return "Analyze this conversation interaction:

User Message: {$userMessage}
WIDDX Response: {$widdxResponse}
Language: {$language}

Extract:
1. User preferences mentioned
2. Topics of interest
3. Communication style indicators
4. Emotional tone
5. Learning opportunities for WIDDX

Respond in JSON format with these fields:
- preferences: array of user preferences
- topics: array of topics discussed
- communication_style: object with style indicators
- emotional_tone: string
- learning_opportunities: array of what WIDDX can learn";
    }

    private function getAnalysisSystemPrompt(string $language): string
    {
        $prompts = [
            'en' => "You are an expert conversation analyst. Analyze conversations to extract learning opportunities and user insights.",
            'ar' => "أنت خبير في تحليل المحادثات. قم بتحليل المحادثات لاستخراج فرص التعلم ورؤى المستخدم.",
        ];

        return $prompts[$language] ?? $prompts['en'];
    }

    private function parseAnalysisResponse(string $response, string $language): array
    {
        try {
            // Try to extract JSON from response
            if (preg_match('/\{.*\}/s', $response, $matches)) {
                $data = json_decode($matches[0], true);
                if (is_array($data)) {
                    return $data;
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to parse analysis response', ['error' => $e->getMessage()]);
        }

        return $this->getFallbackAnalysis('', '', $language);
    }

    private function getFallbackAnalysis(string $userMessage, string $widdxResponse, string $language): array
    {
        return [
            'preferences' => [],
            'topics' => [],
            'communication_style' => ['formality' => 'neutral'],
            'emotional_tone' => 'neutral',
            'learning_opportunities' => [],
        ];
    }

    private function extractPreferenceContext(string $message, string $pattern, string $language): ?string
    {
        $position = stripos($message, $pattern);
        if ($position === false) return null;

        // Extract context around the preference indicator
        $start = max(0, $position - 10);
        $length = min(100, strlen($message) - $start);
        
        return substr($message, $start, $length);
    }

    private function updateLanguageExpertise(string $language): void
    {
        $expertise = PersonalityEvolution::aspect('language_expertise')->first();
        
        if (!$expertise) {
            $expertise = PersonalityEvolution::create([
                'aspect' => 'language_expertise',
                'current_state' => [$language => 1],
                'stability_score' => 0.8,
                'last_updated_at' => now(),
            ]);
        } else {
            $currentState = $expertise->current_state;
            $currentState[$language] = ($currentState[$language] ?? 0) + 1;
            
            $expertise->updateAspect($currentState, "Language usage increment for {$language}");
        }
    }
}
