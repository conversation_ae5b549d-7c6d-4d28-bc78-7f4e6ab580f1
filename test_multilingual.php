<?php

echo "Testing WIDDX AI Multilingual Support\n";
echo "=====================================\n\n";

// Test Arabic message
echo "1. Testing Arabic Chat:\n";
$url = 'http://127.0.0.1:8000/api/chat';
$data = [
    'message' => 'مرحبا، كيف حالك؟ أريد أن أعرف عن الذكاء الاصطناعي.',
    'session_id' => 'test-arabic-session'
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "   Error making Arabic chat request\n";
    print_r($http_response_header);
} else {
    $response = json_decode($result, true);
    echo "   Success: " . ($response['success'] ? 'Yes' : 'No') . "\n";
    if ($response['success']) {
        echo "   Response language: " . ($response['metadata']['detected_language'] ?? 'N/A') . "\n";
        echo "   Language confidence: " . ($response['metadata']['language_confidence'] ?? 'N/A') . "\n";
        echo "   Response preview: " . substr($response['message'], 0, 100) . "...\n";
        echo "   Processing time: " . ($response['processing_time'] ?? 'N/A') . "s\n";

        // Check if response is in Arabic
        $isArabic = preg_match('/[\x{0600}-\x{06FF}]/u', $response['message']);
        echo "   Response contains Arabic: " . ($isArabic ? 'Yes' : 'No') . "\n";
    } else {
        echo "   Error: " . ($response['error'] ?? 'Unknown error') . "\n";
    }
}

echo "\n";

// Test French message
echo "2. Testing French Chat:\n";
$data = [
    'message' => 'Bonjour, comment allez-vous? Pouvez-vous me parler en français?',
    'session_id' => 'test-french-session'
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "   Error making French chat request\n";
    print_r($http_response_header);
} else {
    $response = json_decode($result, true);
    echo "   Success: " . ($response['success'] ? 'Yes' : 'No') . "\n";
    if ($response['success']) {
        echo "   Response language: " . ($response['metadata']['detected_language'] ?? 'N/A') . "\n";
        echo "   Language confidence: " . ($response['metadata']['language_confidence'] ?? 'N/A') . "\n";
        echo "   Response preview: " . substr($response['content'], 0, 100) . "...\n";

        // Check if response is in French
        $isFrench = preg_match('/\b(bonjour|merci|comment|français|très|bien)\b/i', $response['content']);
        echo "   Response contains French: " . ($isFrench ? 'Yes' : 'No') . "\n";
    } else {
        echo "   Error: " . ($response['error'] ?? 'Unknown error') . "\n";
    }
}

echo "\n";

// Test Spanish message
echo "3. Testing Spanish Chat:\n";
$data = [
    'message' => 'Hola, ¿cómo estás? ¿Puedes responder en español?',
    'session_id' => 'test-spanish-session'
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "   Error making Spanish chat request\n";
    print_r($http_response_header);
} else {
    $response = json_decode($result, true);
    echo "   Success: " . ($response['success'] ? 'Yes' : 'No') . "\n";
    if ($response['success']) {
        echo "   Response language: " . ($response['metadata']['detected_language'] ?? 'N/A') . "\n";
        echo "   Language confidence: " . ($response['metadata']['language_confidence'] ?? 'N/A') . "\n";
        echo "   Response preview: " . substr($response['content'], 0, 100) . "...\n";

        // Check if response is in Spanish
        $isSpanish = preg_match('/\b(hola|gracias|cómo|español|muy|bien)\b/i', $response['content']);
        echo "   Response contains Spanish: " . ($isSpanish ? 'Yes' : 'No') . "\n";
    } else {
        echo "   Error: " . ($response['error'] ?? 'Unknown error') . "\n";
    }
}

echo "\nMultilingual testing completed!\n";
