<?php

echo "Testing WIDDX AI Voice Services\n";
echo "===============================\n\n";

// Test Text-to-Speech (English)
echo "1. Testing Text-to-Speech (English):\n";
$url = 'http://127.0.0.1:8000/api/features/text-to-speech';
$data = [
    'text' => 'Hello, this is a test of the text to speech functionality.',
    'language' => 'en',
    'voice' => 'default',
    'speed' => 1.0
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "   Error making TTS request\n";
    print_r($http_response_header);
} else {
    $response = json_decode($result, true);
    echo "   Success: " . ($response['success'] ? 'Yes' : 'No') . "\n";
    if ($response['success']) {
        echo "   Audio URL: " . substr($response['audio_url'], 0, 50) . "...\n";
        echo "   Duration: " . ($response['duration'] ?? 'N/A') . " seconds\n";
        echo "   Provider: " . ($response['provider'] ?? 'N/A') . "\n";
    } else {
        echo "   Error: " . ($response['error'] ?? 'Unknown error') . "\n";
        if (isset($response['arabic_error'])) {
            echo "   Arabic Error: " . $response['arabic_error'] . "\n";
        }
    }
}

echo "\n";

// Test Text-to-Speech (Arabic)
echo "2. Testing Text-to-Speech (Arabic):\n";
$data = [
    'text' => 'مرحبا، هذا اختبار لخدمة تحويل النص إلى كلام.',
    'language' => 'ar',
    'voice' => 'default',
    'speed' => 1.0
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "   Error making Arabic TTS request\n";
    print_r($http_response_header);
} else {
    $response = json_decode($result, true);
    echo "   Success: " . ($response['success'] ? 'Yes' : 'No') . "\n";
    if ($response['success']) {
        echo "   Audio URL: " . substr($response['audio_url'], 0, 50) . "...\n";
        echo "   Duration: " . ($response['duration'] ?? 'N/A') . " seconds\n";
        echo "   Provider: " . ($response['provider'] ?? 'N/A') . "\n";
    } else {
        echo "   Error: " . ($response['error'] ?? 'Unknown error') . "\n";
        if (isset($response['arabic_error'])) {
            echo "   Arabic Error: " . $response['arabic_error'] . "\n";
        }
    }
}

echo "\n";

// Test Voice Capabilities
echo "3. Testing Voice Capabilities:\n";
$url = 'http://127.0.0.1:8000/api/features/capabilities';

$options = [
    'http' => [
        'header' => [
            'Accept: application/json'
        ],
        'method' => 'GET'
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "   Error getting capabilities\n";
    print_r($http_response_header);
} else {
    $response = json_decode($result, true);
    if (isset($response['voice'])) {
        echo "   Voice capabilities available: Yes\n";
        echo "   TTS supported: " . ($response['voice']['tts_supported'] ? 'Yes' : 'No') . "\n";
        echo "   STT supported: " . ($response['voice']['stt_supported'] ? 'Yes' : 'No') . "\n";
        echo "   Languages: " . implode(', ', $response['voice']['supported_languages'] ?? []) . "\n";
    } else {
        echo "   Voice capabilities not found\n";
    }
}

echo "\nVoice testing completed!\n";
